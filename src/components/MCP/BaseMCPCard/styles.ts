import styled from '@emotion/styled';
import {css} from '@emotion/css';
import {colors} from '@/constants/colors';
import bg from '@/assets/mcp/cardBg.png';
import vipbg from '@/assets/mcp/cardVipBg.png';

export const containerCss = ({official}: {official?: boolean}) => css`
    padding: 17px 24px 12px;
    position: relative;
    height: 213px;
    border-radius: 6px;
    &:hover {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        z-index: 2;
        background: url(${official ? vipbg : bg}) no-repeat;
        background-size: cover;
        height: 264px;
        .hover-actions {
            opacity: 1;
            min-height: 51px;
            padding: 0 24px 20px;
        }
    }
`;

export const hoverActionsStyle = css`
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    max-height: 0;
    opacity: 0;
    transition: all 0.3s ease;
`;

export const DescriptionContainer = styled.div`
    margin: 17px 0 12px;
    font-size: 14px;
    line-height: 22px;
    position: relative;
    height: 44px;
    color: #545454;
    overflow: hidden;
`;

export const DescriptionText = styled.div`
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    word-break: break-word;
    overflow: hidden;
`;

export const EllipsisOverlay = styled.div`
    position: absolute;
    bottom: 9px;
    right: 12px;
    padding-left: 10px;
    pointer-events: none;
`;

export const cardContentStyle = {
    overflow: 'hidden',
    flex: 1,
};

export const protocolTextStyle = {
    color: '#8F8F8F',
    fontSize: 12,
    lineHeight: '18px',
};

export const dividerStyle = {
    margin: '16px 0 8px',
};

export const statsContainerStyle = css`
    color: ${colors['gray-7']};
    font-size: 12px;
    line-height: 20px;
    transition: color 0.2s ease;
    &:hover {
        color: ${colors.primary};
    }
`;

export const iconStyle = {
    width: 14,
    height: 14,
};

export const actionButtonStyle = {
    fontSize: '12px',
    lineHeight: '20px',
    padding: 0,
    height: 20,
    gap: 4,
    color: '#545454',
};

export const fullWidthButtonStyle = {
    background: 'var(--Tokens-, #F2F2F2)',
    border: 'none',
    width: '100%',
    height: '32px',
    gap: 4,
};

export const formatCount = (count: number): string => {
    if (count >= 10000) {
        return `${Math.floor(count / 10000)}w+`;
    }
    if (count >= 1000) {
        return `${Math.floor(count / 1000)}k+`;
    }
    return count.toString();
};

export const actionButtonHoverStyle = css`
    flex: 1;
    border-radius: 4px;
    border: 1px solid #BFBFBF;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &:hover {
        background-color: #E6E6E6 !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transform: translateY(-1px);
    }
`;
